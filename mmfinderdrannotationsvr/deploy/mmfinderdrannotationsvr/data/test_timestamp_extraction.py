#!/usr/bin/env python3
"""
测试从page_session_id中提取report_timestamp_的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.utils.batch_simplify_xml import extract_report_timestamp

def test_extract_report_timestamp():
    """测试时间戳提取功能"""
    
    # 测试用例
    test_cases = [
        {
            "input": "hash=1749546446&ts=1752044772360&host=&version=788529166&device=2#262539389#1752044811751#CGI3_16",
            "expected": 1752044811751,
            "description": "标准格式"
        },
        {
            "input": "hash=123&ts=456#789#1234567890123#ABC",
            "expected": 1234567890123,
            "description": "简化格式"
        },
        {
            "input": "some_prefix#999#9876543210987#suffix",
            "expected": 9876543210987,
            "description": "只有#分隔的部分"
        },
        {
            "input": "no_timestamp_here",
            "expected": None,
            "description": "没有时间戳"
        },
        {
            "input": "",
            "expected": None,
            "description": "空字符串"
        },
        {
            "input": None,
            "expected": None,
            "description": "None值"
        }
    ]
    
    print("开始测试时间戳提取功能...")
    print("=" * 50)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        input_val = test_case["input"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        try:
            result = extract_report_timestamp(input_val)
            
            if result == expected:
                print(f"✓ 测试 {i}: {description} - 通过")
                print(f"  输入: {input_val}")
                print(f"  结果: {result}")
            else:
                print(f"✗ 测试 {i}: {description} - 失败")
                print(f"  输入: {input_val}")
                print(f"  期望: {expected}")
                print(f"  实际: {result}")
                all_passed = False
                
        except Exception as e:
            print(f"✗ 测试 {i}: {description} - 异常")
            print(f"  输入: {input_val}")
            print(f"  异常: {e}")
            all_passed = False
        
        print()
    
    print("=" * 50)
    if all_passed:
        print("✓ 所有测试通过!")
    else:
        print("✗ 部分测试失败!")
    
    return all_passed

if __name__ == "__main__":
    test_extract_report_timestamp()

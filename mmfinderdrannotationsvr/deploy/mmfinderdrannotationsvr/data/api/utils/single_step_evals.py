"""

"""
from rawins import Rawins
import pandas as pd

# db_conf
mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_z<PERSON><PERSON>",
    "charset": 'utf8'
}

# clickhouse conf
ch_conf = {
"host":'**************',
"port": 9000,
"user": 'longvideo_luban',
"password": 'GZqtkM9yG4N6ZKbYsa7r',
"database": 'default' ,
"send_receive_timeout": 120
}

rawins = Rawins(ch_conf, mysql_conf)
rawins.register("jinweiguo")


pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option("display.max_colwidth", None)


def get_eval_versions():
    """
    获取评测版本
    """
    sql = """
        SELECT distinct version_id_ FROM dw_luban.ods_luban_topic_xcx_eval_report
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_eval_version_info(version_id):
    """
    获取评测版本信息
    """
    sql = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id}'
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_eval_version_info_count(version_id):
    """
    获取评测版本信息总数
    """
    sql = f"""
        SELECT COUNT(*) as total FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id}'
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df.iloc[0]['total'] if len(df) > 0 else 0


def get_eval_version_info_paginated(version_id, page=1, page_size=10):
    """
    分页获取评测版本信息

    Args:
        version_id: 版本ID
        page: 页码，从1开始
        page_size: 每页大小

    Returns:
        DataFrame: 分页后的评测版本信息
    """
    offset = (page - 1) * page_size
    sql = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id}'
        ORDER BY timestamp_ DESC
        LIMIT {page_size} OFFSET {offset}
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


if __name__ == "__main__":
    df = get_eval_versions()
    print(df.head(5))
    version_info = get_eval_version_info("1752031761328752772")
    print(version_info.head(5))

"""

"""
from rawins import Rawins
import pandas as pd

# db_conf
mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_z<PERSON><PERSON>",
    "charset": 'utf8'
}

# clickhouse conf
ch_conf = {
"host":'**************',
"port": 9000,
"user": 'longvideo_luban',
"password": 'GZqtkM9yG4N6ZKbYsa7r',
"database": 'default' ,
"send_receive_timeout": 120
}

rawins = Rawins(ch_conf, mysql_conf)
rawins.register("jinweiguo")


pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option("display.max_colwidth", None)


def get_eval_versions():
    """
    获取评测版本
    """
    sql = """
        SELECT distinct version_id_ FROM dw_luban.ods_luban_topic_xcx_eval_report
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_eval_version_info(version_id):
    """
    获取评测版本信息
    """
    sql = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id}'
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_eval_version_info_count(version_id, origin_id=None, instruction=None, appid=None, is_success=None):
    """
    获取评测版本信息总数

    Args:
        version_id: 版本ID
        origin_id: 原始ID筛选条件
        instruction: 指令筛选条件
        appid: 应用ID筛选条件
        is_success: 是否只返回成功的operation（在应用层筛选，此参数暂时不在SQL中使用）
    """
    where_conditions = [f"version_id_ = '{version_id}'"]

    if origin_id:
        where_conditions.append(f"origin_id_ = '{origin_id}'")
    if instruction:
        where_conditions.append(f"instruction_ LIKE '%{instruction}%'")
    if appid:
        where_conditions.append(f"appid_ = '{appid}'")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
        SELECT COUNT(*) as total FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE {where_clause}
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df.iloc[0]['total'] if len(df) > 0 else 0


def get_eval_version_info_paginated(version_id, page=1, page_size=10, origin_id=None, instruction=None, appid=None, is_success=None):
    """
    分页获取评测版本信息

    Args:
        version_id: 版本ID
        page: 页码，从1开始
        page_size: 每页大小
        origin_id: 原始ID筛选条件
        instruction: 指令筛选条件
        appid: 应用ID筛选条件
        is_success: 是否只返回成功的operation（在应用层筛选，此参数暂时不在SQL中使用）

    Returns:
        DataFrame: 分页后的评测版本信息
    """
    where_conditions = [f"version_id_ = '{version_id}'"]

    if origin_id:
        where_conditions.append(f"origin_id_ = '{origin_id}'")
    if instruction:
        where_conditions.append(f"instruction_ LIKE '%{instruction}%'")
    if appid:
        where_conditions.append(f"appid_ = '{appid}'")

    where_clause = " AND ".join(where_conditions)
    offset = (page - 1) * page_size

    sql = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE {where_clause}
        ORDER BY timestamp_ DESC
        LIMIT {page_size} OFFSET {offset}
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_eval_version_info_with_filters(version_id, origin_id=None, instruction=None, appid=None):
    """
    获取带筛选条件的评测版本信息（不分页，用于应用层筛选）

    Args:
        version_id: 版本ID
        origin_id: 原始ID筛选条件
        instruction: 指令筛选条件
        appid: 应用ID筛选条件

    Returns:
        DataFrame: 筛选后的评测版本信息
    """
    where_conditions = [f"version_id_ = '{version_id}'"]

    if origin_id:
        where_conditions.append(f"origin_id_ = '{origin_id}'")
    if instruction:
        where_conditions.append(f"instruction_ LIKE '%{instruction}%'")
    if appid:
        where_conditions.append(f"appid_ = '{appid}'")

    where_clause = " AND ".join(where_conditions)

    sql = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE {where_clause}
        ORDER BY timestamp_ DESC
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return df


def get_version_origin_ids(version_id):
    """
    获取指定版本的所有origin_id

    Args:
        version_id: 版本ID

    Returns:
        set: 该版本的所有origin_id集合
    """
    sql = f"""
        SELECT DISTINCT origin_id_ FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id}'
        """
    df = rawins.query_dataframe("{}".format(sql), pretty=False, use_alias=False)
    return set(df['origin_id_'].tolist()) if len(df) > 0 else set()


def get_version_comparison_data(version_id1, version_id2, common_origin_ids):
    """
    获取两个版本中指定origin_id的对比数据

    Args:
        version_id1: 第一个版本ID
        version_id2: 第二个版本ID
        common_origin_ids: 共同的origin_id列表

    Returns:
        tuple: (version1_df, version2_df)
    """
    if not common_origin_ids:
        # 返回空的DataFrame
        import pandas as pd
        empty_df = pd.DataFrame()
        return empty_df, empty_df

    origin_ids_str = "', '".join(common_origin_ids)

    sql1 = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id1}' AND origin_id_ IN ('{origin_ids_str}')
        ORDER BY origin_id_, timestamp_ DESC
        """

    sql2 = f"""
        SELECT * FROM dw_luban.ods_luban_topic_xcx_eval_report
        WHERE version_id_ = '{version_id2}' AND origin_id_ IN ('{origin_ids_str}')
        ORDER BY origin_id_, timestamp_ DESC
        """

    df1 = rawins.query_dataframe("{}".format(sql1), pretty=False, use_alias=False)
    df2 = rawins.query_dataframe("{}".format(sql2), pretty=False, use_alias=False)

    return df1, df2


if __name__ == "__main__":
    df = get_eval_versions()
    print(df.head(5))
    version_info = get_eval_version_info("1752031761328752772")
    print(version_info.head(5))

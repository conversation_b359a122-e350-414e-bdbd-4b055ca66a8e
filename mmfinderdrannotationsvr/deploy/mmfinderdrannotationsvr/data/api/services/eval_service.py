"""
Eval service for handling evaluation versions
"""

import json
from typing import Optional

from api.models.eval import (
    ComparisonOperationData,
    EvalVersion,
    EvalVersionDetail,
    EvalVersionDetailResponse,
    EvalVersionsResponse,
    VersionComparisonItem,
    VersionComparisonResponse,
)
from api.utils.xcx_eval_report import (
    get_eval_version_info_count,
    get_eval_version_info_paginated,
    get_eval_version_info_with_filters,
    get_eval_versions,
    get_version_comparison_data_with_filters,
)


class EvalService:
    """Service for handling evaluation versions"""

    @staticmethod
    async def get_all_eval_versions() -> EvalVersionsResponse:
        """
        获取所有的评测版本

        Returns:
            EvalVersionsResponse: 包含所有评测版本的响应
        """
        try:
            # 调用现有的函数获取评测版本
            df = get_eval_versions()

            # 将DataFrame转换为响应模型
            versions = []
            for _, row in df.iterrows():
                versions.append(EvalVersion(version_id_=row["version_id_"]))

            return EvalVersionsResponse(versions=versions)

        except Exception as e:
            raise Exception(f"Failed to get eval versions: {str(e)}") from e

    @staticmethod
    async def get_eval_version_detail(
        version_id: str,
        page: int = 1,
        page_size: int = 10,
        origin_id: Optional[str] = None,
        instruction: Optional[str] = None,
        appid: Optional[str] = None,
        is_success: Optional[bool] = None,
    ) -> EvalVersionDetailResponse:
        """
        获取特定评测版本的详细信息

        Args:
            version_id: 版本ID
            page: 页码，从1开始
            page_size: 每页大小
            origin_id: 原始ID筛选条件
            instruction: 指令筛选条件
            appid: 应用ID筛选条件
            is_success: 是否只返回成功的operation（status=2）

        Returns:
            EvalVersionDetailResponse: 评测版本的详细信息
        """
        try:
            if is_success is not None:
                # 带is_success筛选：使用应用层分页
                return await EvalService._get_with_app_layer_pagination(
                    version_id,
                    page,
                    page_size,
                    origin_id,
                    instruction,
                    appid,
                    is_success,
                )
            else:
                # 不带is_success筛选：使用数据库分页（高性能）
                return await EvalService._get_with_db_pagination(
                    version_id, page, page_size, origin_id, instruction, appid
                )

        except Exception as e:
            raise Exception(
                f"Failed to get eval version info for {version_id}: {str(e)}"
            ) from e

    @staticmethod
    async def _get_with_db_pagination(
        version_id: str,
        page: int,
        page_size: int,
        origin_id: Optional[str] = None,
        instruction: Optional[str] = None,
        appid: Optional[str] = None,
    ) -> EvalVersionDetailResponse:
        """使用数据库分页（高性能，适用于不需要is_success筛选的情况）"""
        import math

        # 先获取总数
        total = get_eval_version_info_count(version_id, origin_id, instruction, appid)

        # 计算总页数
        total_pages = math.ceil(total / page_size) if page_size > 0 else 0

        # 分页获取数据
        paginated_df = get_eval_version_info_paginated(
            version_id, page, page_size, origin_id, instruction, appid
        )

        # 将DataFrame转换为结构化的详细信息列表
        details = []
        for _, row in paginated_df.iterrows():
            # 解析config_字段
            operations = []
            if row.get("config_") and row["config_"].strip():
                try:
                    parsed_operations = json.loads(row["config_"])
                    if isinstance(parsed_operations, list):
                        operations = parsed_operations
                    elif isinstance(parsed_operations, dict):
                        operations = [parsed_operations]
                    else:
                        operations = []
                except (json.JSONDecodeError, TypeError):
                    operations = []

            detail = EvalVersionDetail(
                day_=str(row.get("day_", "")),
                hour_=str(row.get("hour_", "")),
                minute_=str(row.get("minute_", "")),
                second_=str(row.get("second_", "")),
                insert_ch_time_=str(row.get("insert_ch_time_", "")),
                origin_id_=str(row.get("origin_id_", "")),
                instruction_=str(row.get("instruction_", "")),
                appid_=str(row.get("appid_", "")),
                version_id_=str(row.get("version_id_", "")),
                config_=str(row.get("config_", "")),
                timestamp_=str(row.get("timestamp_", "")),
                operations=operations,
            )
            details.append(detail)

        return EvalVersionDetailResponse(
            details=details,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    @staticmethod
    async def _get_with_app_layer_pagination(
        version_id: str,
        page: int,
        page_size: int,
        origin_id: Optional[str] = None,
        instruction: Optional[str] = None,
        appid: Optional[str] = None,
        is_success: Optional[bool] = None,
    ) -> EvalVersionDetailResponse:
        """使用应用层分页（适用于需要is_success筛选的情况）"""
        import math

        # 获取所有符合基础条件的数据
        all_df = get_eval_version_info_with_filters(
            version_id, origin_id, instruction, appid
        )

        # 在应用层处理数据并筛选
        all_details = []
        for _, row in all_df.iterrows():
            # 解析config_字段
            operations = []
            if row.get("config_") and row["config_"].strip():
                try:
                    parsed_operations = json.loads(row["config_"])

                    # 如果is_success为True，只保留status=2的operations
                    if is_success:
                        if isinstance(parsed_operations, list):
                            operations = [
                                op for op in parsed_operations if op.get("status") == 2
                            ]
                        elif (
                            isinstance(parsed_operations, dict)
                            and parsed_operations.get("status") == 2
                        ):
                            operations = [parsed_operations]
                        else:
                            operations = []
                    else:
                        # 如果不筛选，保留所有operations
                        if isinstance(parsed_operations, list):
                            operations = parsed_operations
                        elif isinstance(parsed_operations, dict):
                            operations = [parsed_operations]
                        else:
                            operations = []

                except (json.JSONDecodeError, TypeError):
                    # 如果解析失败，保持空列表
                    operations = []

            # 如果is_success为True且没有成功的operations，跳过这条记录
            if is_success and not operations:
                continue

            detail = EvalVersionDetail(
                day_=str(row.get("day_", "")),
                hour_=str(row.get("hour_", "")),
                minute_=str(row.get("minute_", "")),
                second_=str(row.get("second_", "")),
                insert_ch_time_=str(row.get("insert_ch_time_", "")),
                origin_id_=str(row.get("origin_id_", "")),
                instruction_=str(row.get("instruction_", "")),
                appid_=str(row.get("appid_", "")),
                version_id_=str(row.get("version_id_", "")),
                config_=str(row.get("config_", "")),
                timestamp_=str(row.get("timestamp_", "")),
                operations=operations,
            )
            all_details.append(detail)

        # 计算筛选后的总数
        total = len(all_details)

        # 计算总页数
        total_pages = math.ceil(total / page_size) if page_size > 0 else 0

        # 应用层分页
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_details = all_details[start_idx:end_idx]

        return EvalVersionDetailResponse(
            details=paginated_details,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    @staticmethod
    async def compare_versions(
        control_version_id: str,
        experiment_version_id: str,
        page: int = 1,
        page_size: int = 10,
        origin_id: Optional[str] = None,
        instruction: Optional[str] = None,
        appid: Optional[str] = None,
    ) -> VersionComparisonResponse:
        """
        对比两个版本，返回origin_id交集部分的数据

        Args:
            control_version_id: 对照组版本ID
            experiment_version_id: 实验组版本ID
            page: 页码，从1开始
            page_size: 每页大小
            origin_id: 原始ID筛选条件
            instruction: 指令筛选条件
            appid: 应用ID筛选条件

        Returns:
            VersionComparisonResponse: 版本对比结果
        """
        try:
            # 获取两个版本的数据（应用筛选条件）
            control_df, experiment_df = get_version_comparison_data_with_filters(
                control_version_id, experiment_version_id, origin_id, instruction, appid
            )

            # 获取两个版本的origin_id集合（基于筛选后的数据）
            control_origin_ids = (
                set(control_df["origin_id_"].tolist()) if len(control_df) > 0 else set()
            )
            experiment_origin_ids = (
                set(experiment_df["origin_id_"].tolist())
                if len(experiment_df) > 0
                else set()
            )

            # 计算交集
            common_origin_ids = list(
                control_origin_ids.intersection(experiment_origin_ids)
            )
            common_origin_ids.sort()  # 排序以保证结果的一致性

            if not common_origin_ids:
                # 如果没有交集，返回空结果
                import math

                return VersionComparisonResponse(
                    control_version_id=control_version_id,
                    experiment_version_id=experiment_version_id,
                    common_origin_ids=[],
                    comparison_data=[],
                    total=0,
                    page=page,
                    page_size=page_size,
                    total_pages=0,
                )

            # 按origin_id分组处理数据，收集所有有差异的对比项
            all_comparison_data = []

            for current_origin_id in common_origin_ids:
                # 获取该origin_id在两个版本中的数据
                control_rows = (
                    control_df[control_df["origin_id_"] == current_origin_id]
                    if len(control_df) > 0
                    else control_df.iloc[0:0]
                )  # 空DataFrame
                experiment_rows = (
                    experiment_df[experiment_df["origin_id_"] == current_origin_id]
                    if len(experiment_df) > 0
                    else experiment_df.iloc[0:0]
                )  # 空DataFrame

                # 收集对照组和实验组的所有operations用于状态对比
                control_operations_map = {}  # {operation_index: operation}
                experiment_operations_map = {}  # {operation_index: operation}

                # 处理对照组的数据，收集operations
                if len(control_rows) > 0:
                    for _, row in control_rows.iterrows():
                        if row.get("config_") and row["config_"].strip():
                            try:
                                parsed_operations = json.loads(row["config_"])
                                if isinstance(parsed_operations, list):
                                    for i, op in enumerate(parsed_operations):
                                        control_operations_map[i] = op
                                elif isinstance(parsed_operations, dict):
                                    control_operations_map[0] = parsed_operations
                            except (json.JSONDecodeError, TypeError):
                                pass

                # 处理实验组的数据，收集operations
                if len(experiment_rows) > 0:
                    for _, row in experiment_rows.iterrows():
                        if row.get("config_") and row["config_"].strip():
                            try:
                                parsed_operations = json.loads(row["config_"])
                                if isinstance(parsed_operations, list):
                                    for i, op in enumerate(parsed_operations):
                                        experiment_operations_map[i] = op
                                elif isinstance(parsed_operations, dict):
                                    experiment_operations_map[0] = parsed_operations
                            except (json.JSONDecodeError, TypeError):
                                pass

                # 找出状态不同的operations
                different_operations = []
                all_operation_indices = set(control_operations_map.keys()) | set(
                    experiment_operations_map.keys()
                )

                for op_index in all_operation_indices:
                    control_op = control_operations_map.get(op_index)
                    experiment_op = experiment_operations_map.get(op_index)

                    # 获取状态值
                    control_status = control_op.get("status") if control_op else None
                    experiment_status = (
                        experiment_op.get("status") if experiment_op else None
                    )

                    # 如果状态不同，则保留这个operation
                    if control_status != experiment_status:
                        different_operations.append(
                            {
                                "index": op_index,
                                "control_operation": control_op,
                                "experiment_operation": experiment_op,
                            }
                        )

                # 如果没有状态不同的operations，跳过这个origin_id
                if not different_operations:
                    continue

                # 提取共同的字段（从第一行数据中获取）
                instruction_ = ""
                appid_ = ""

                # 从对照组或实验组的第一行数据中提取共同字段
                if len(control_rows) > 0:
                    first_row = control_rows.iloc[0]
                    instruction_ = str(first_row.get("instruction_", ""))
                    appid_ = str(first_row.get("appid_", ""))
                elif len(experiment_rows) > 0:
                    first_row = experiment_rows.iloc[0]
                    instruction_ = str(first_row.get("instruction_", ""))
                    appid_ = str(first_row.get("appid_", ""))

                # 转换为ComparisonOperationData对象
                control_data = []
                experiment_data = []

                # 处理对照组的数据，只保留状态不同的operations
                if len(control_rows) > 0:
                    for _, row in control_rows.iterrows():
                        # 只保留状态不同的operations
                        filtered_operations = [
                            diff_op["control_operation"]
                            for diff_op in different_operations
                            if diff_op["control_operation"] is not None
                        ]

                        if (
                            filtered_operations
                        ):  # 只有当有状态不同的operations时才创建detail
                            detail = ComparisonOperationData(
                                day_=str(row.get("day_", "")),
                                hour_=str(row.get("hour_", "")),
                                minute_=str(row.get("minute_", "")),
                                second_=str(row.get("second_", "")),
                                insert_ch_time_=str(row.get("insert_ch_time_", "")),
                                version_id_=str(row.get("version_id_", "")),
                                config_=str(row.get("config_", "")),
                                timestamp_=str(row.get("timestamp_", "")),
                                operations=filtered_operations,
                            )
                            control_data.append(detail)

                # 处理实验组的数据，只保留状态不同的operations
                if len(experiment_rows) > 0:
                    for _, row in experiment_rows.iterrows():
                        # 只保留状态不同的operations
                        filtered_operations = [
                            diff_op["experiment_operation"]
                            for diff_op in different_operations
                            if diff_op["experiment_operation"] is not None
                        ]

                        if (
                            filtered_operations
                        ):  # 只有当有状态不同的operations时才创建detail
                            detail = ComparisonOperationData(
                                day_=str(row.get("day_", "")),
                                hour_=str(row.get("hour_", "")),
                                minute_=str(row.get("minute_", "")),
                                second_=str(row.get("second_", "")),
                                insert_ch_time_=str(row.get("insert_ch_time_", "")),
                                version_id_=str(row.get("version_id_", "")),
                                config_=str(row.get("config_", "")),
                                timestamp_=str(row.get("timestamp_", "")),
                                operations=filtered_operations,
                            )
                            experiment_data.append(detail)

                # 只有当对照组和实验组都有数据时才创建对比项
                if control_data and experiment_data:
                    comparison_item = VersionComparisonItem(
                        origin_id_=current_origin_id,
                        instruction_=instruction_,
                        appid_=appid_,
                        control_data=control_data,
                        experiment_data=experiment_data,
                    )
                    all_comparison_data.append(comparison_item)

            # 计算分页信息
            import math

            total_items = len(all_comparison_data)
            total_pages = math.ceil(total_items / page_size) if page_size > 0 else 0

            # 应用分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_comparison_data = all_comparison_data[start_idx:end_idx]

            return VersionComparisonResponse(
                control_version_id=control_version_id,
                experiment_version_id=experiment_version_id,
                common_origin_ids=[
                    item.origin_id_ for item in paginated_comparison_data
                ],  # 当前页的origin_id
                comparison_data=paginated_comparison_data,
                total=total_items,  # 总的对比项数量
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )

        except Exception as e:
            raise Exception(
                f"Failed to compare versions {control_version_id} and {experiment_version_id}: {str(e)}"
            ) from e

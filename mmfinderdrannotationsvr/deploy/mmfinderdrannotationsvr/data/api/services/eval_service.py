"""
Eval service for handling evaluation versions
"""
from api.utils.single_step_evals import get_eval_versions, get_eval_version_info
from api.models.response import EvalVersion, EvalVersionsResponse


class EvalService:
    """Service for handling evaluation versions"""

    @staticmethod
    async def get_all_eval_versions() -> EvalVersionsResponse:
        """
        获取所有的评测版本

        Returns:
            EvalVersionsResponse: 包含所有评测版本的响应
        """
        try:
            # 调用现有的函数获取评测版本
            df = get_eval_versions()

            # 将DataFrame转换为响应模型
            versions = []
            for _, row in df.iterrows():
                versions.append(EvalVersion(version_id_=row['version_id_']))

            return EvalVersionsResponse(versions=versions)

        except Exception as e:
            raise Exception(f"Failed to get eval versions: {str(e)}") from e

    @staticmethod
    async def get_eval_version_detail(version_id: str):
        """
        获取特定评测版本的详细信息

        Args:
            version_id: 版本ID

        Returns:
            评测版本的详细信息
        """
        try:
            df = get_eval_version_info(version_id)

            # 将DataFrame转换为字典列表
            result = df.to_dict('records')
            return result

        except Exception as e:
            raise Exception(f"Failed to get eval version info for {version_id}: {str(e)}") from e

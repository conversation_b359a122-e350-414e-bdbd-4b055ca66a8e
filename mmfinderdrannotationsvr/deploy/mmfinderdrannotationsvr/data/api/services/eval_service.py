"""
Eval service for handling evaluation versions
"""
import json
from api.utils.single_step_evals import get_eval_versions, get_eval_version_info_count, get_eval_version_info_paginated
from api.models.eval import EvalVersion, EvalVersionsResponse, EvalVersionDetail, EvalVersionDetailResponse


class EvalService:
    """Service for handling evaluation versions"""

    @staticmethod
    async def get_all_eval_versions() -> EvalVersionsResponse:
        """
        获取所有的评测版本

        Returns:
            EvalVersionsResponse: 包含所有评测版本的响应
        """
        try:
            # 调用现有的函数获取评测版本
            df = get_eval_versions()

            # 将DataFrame转换为响应模型
            versions = []
            for _, row in df.iterrows():
                versions.append(EvalVersion(version_id_=row['version_id_']))

            return EvalVersionsResponse(versions=versions)

        except Exception as e:
            raise Exception(f"Failed to get eval versions: {str(e)}") from e

    @staticmethod
    async def get_eval_version_detail(version_id: str, page: int = 1, page_size: int = 10) -> EvalVersionDetailResponse:
        """
        获取特定评测版本的详细信息

        Args:
            version_id: 版本ID
            page: 页码，从1开始
            page_size: 每页大小

        Returns:
            EvalVersionDetailResponse: 评测版本的详细信息
        """
        try:
            # 先获取总数
            total = get_eval_version_info_count(version_id)

            # 计算总页数
            import math
            total_pages = math.ceil(total / page_size) if page_size > 0 else 0

            # 分页获取数据
            paginated_df = get_eval_version_info_paginated(version_id, page, page_size)

            # 将DataFrame转换为结构化的详细信息列表
            details = []
            for _, row in paginated_df.iterrows():
                # 解析config_字段
                operations = []
                if row.get('config_') and row['config_'].strip():
                    try:
                        operations = json.loads(row['config_'])
                    except (json.JSONDecodeError, TypeError):
                        # 如果解析失败，保持空列表
                        operations = []

                detail = EvalVersionDetail(
                    day_=str(row.get('day_', '')),
                    hour_=str(row.get('hour_', '')),
                    minute_=str(row.get('minute_', '')),
                    second_=str(row.get('second_', '')),
                    insert_ch_time_=str(row.get('insert_ch_time_', '')),
                    origin_id_=str(row.get('origin_id_', '')),
                    instruction_=str(row.get('instruction_', '')),
                    appid_=str(row.get('appid_', '')),
                    version_id_=str(row.get('version_id_', '')),
                    config_=str(row.get('config_', '')),
                    timestamp_=str(row.get('timestamp_', '')),
                    operations=operations
                )
                details.append(detail)

            return EvalVersionDetailResponse(
                details=details,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages
            )

        except Exception as e:
            raise Exception(f"Failed to get eval version info for {version_id}: {str(e)}") from e

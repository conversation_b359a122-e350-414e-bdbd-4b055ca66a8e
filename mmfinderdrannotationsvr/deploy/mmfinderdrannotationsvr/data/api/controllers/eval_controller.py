"""
Eval controller for handling evaluation version HTTP requests
"""
from typing import Optional

from fastapi import APIRouter, HTTPException, Query

from api.services.eval_service import EvalService
from api.models.response import StandardResponse
from api.models.eval import EvalVersionsResponse, EvalVersionDetailResponse

router = APIRouter()


@router.get("/versions", response_model=StandardResponse[EvalVersionsResponse])
async def get_eval_versions():
    """
    获取所有的评测版本

    Returns:
        StandardResponse[EvalVersionsResponse]: 包含所有评测版本的标准响应
    """
    try:
        versions_response = await EvalService.get_all_eval_versions()
        return StandardResponse(data=versions_response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/versions/{version_id}", response_model=StandardResponse[EvalVersionDetailResponse])
async def get_eval_version_detail(
    version_id: str,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=100, description="每页大小，最大100"),
    origin_id: Optional[str] = Query(None, description="原始ID筛选"),
    instruction: Optional[str] = Query(None, description="指令内容筛选（模糊匹配）"),
    appid: Optional[str] = Query(None, description="应用ID筛选"),
    is_success: Optional[bool] = Query(None, description="是否只返回成功的operation（status=2）")
):
    """
    获取特定评测版本的详细信息

    Args:
        version_id: 版本ID
        page: 页码，从1开始
        page_size: 每页大小，最大100
        origin_id: 原始ID筛选条件
        instruction: 指令内容筛选条件（模糊匹配）
        appid: 应用ID筛选条件
        is_success: 是否只返回成功的operation（status=2）

    Returns:
        StandardResponse[EvalVersionDetailResponse]: 包含评测版本详细信息的标准响应
    """
    try:
        version_detail = await EvalService.get_eval_version_detail(
            version_id, page, page_size, origin_id, instruction, appid, is_success
        )
        return StandardResponse(data=version_detail)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) from e

"""
Eval related data models
"""
from typing import List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime


class EvalVersion(BaseModel):
    """评测版本模型"""
    version_id_: str


class EvalVersionsResponse(BaseModel):
    """评测版本列表响应模型"""
    versions: List[EvalVersion]


class EvalVersionDetail(BaseModel):
    """评测版本详细信息模型"""
    day_: str = Field(..., description="日期，格式：YYYY-MM-DD")
    hour_: str = Field(..., description="小时时间戳，格式：YYYY-MM-DD HH:00:00")
    minute_: str = Field(..., description="分钟时间戳，格式：YYYY-MM-DD HH:MM:00")
    second_: str = Field(..., description="秒级时间戳，格式：YYYY-MM-DD HH:MM:SS")
    insert_ch_time_: str = Field(..., description="插入ClickHouse的时间")
    origin_id_: str = Field(..., description="原始ID")
    instruction_: str = Field(..., description="指令内容")
    appid_: str = Field(..., description="应用ID")
    version_id_: str = Field(..., description="版本ID")
    config_: str = Field(..., description="配置信息，JSON字符串格式")
    timestamp_: str = Field(..., description="时间戳")
    operations: List[Any] = Field(default_factory=list, description="从config_解析得到的操作列表")


class EvalVersionDetailResponse(BaseModel):
    """评测版本详细信息响应模型"""
    details: List[EvalVersionDetail]

"""
Eval related data models
"""

from typing import Any, List

from pydantic import BaseModel, Field


class EvalVersion(BaseModel):
    """评测版本模型"""

    version_id_: str


class EvalVersionsResponse(BaseModel):
    """评测版本列表响应模型"""

    versions: List[EvalVersion]


class EvalVersionDetail(BaseModel):
    """评测版本详细信息模型"""

    day_: str = Field(..., description="日期，格式：YYYY-MM-DD")
    hour_: str = Field(..., description="小时时间戳，格式：YYYY-MM-DD HH:00:00")
    minute_: str = Field(..., description="分钟时间戳，格式：YYYY-MM-DD HH:MM:00")
    second_: str = Field(..., description="秒级时间戳，格式：YYYY-MM-DD HH:MM:SS")
    insert_ch_time_: str = Field(..., description="插入ClickHouse的时间")
    origin_id_: str = Field(..., description="原始ID")
    instruction_: str = Field(..., description="指令内容")
    appid_: str = Field(..., description="应用ID")
    version_id_: str = Field(..., description="版本ID")
    config_: str = Field(..., description="配置信息，JSON字符串格式")
    timestamp_: str = Field(..., description="时间戳")
    operations: List[Any] = Field(
        default_factory=list, description="从config_解析得到的操作列表"
    )


class EvalVersionDetailResponse(BaseModel):
    """评测版本详细信息响应模型"""

    details: List[EvalVersionDetail]
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


class ComparisonOperationData(BaseModel):
    """对比操作数据模型（去除冗余字段）"""

    day_: str = Field(..., description="日期，格式：YYYY-MM-DD")
    hour_: str = Field(..., description="小时时间戳，格式：YYYY-MM-DD HH:00:00")
    minute_: str = Field(..., description="分钟时间戳，格式：YYYY-MM-DD HH:MM:00")
    second_: str = Field(..., description="秒级时间戳，格式：YYYY-MM-DD HH:MM:SS")
    insert_ch_time_: str = Field(..., description="插入ClickHouse的时间")
    version_id_: str = Field(..., description="版本ID")
    config_: str = Field(..., description="配置信息，JSON字符串格式")
    timestamp_: str = Field(..., description="时间戳")
    operations: List[Any] = Field(
        default_factory=list, description="状态不同的操作列表"
    )


class VersionComparisonItem(BaseModel):
    """版本对比项模型"""

    origin_id_: str = Field(..., description="原始ID")
    instruction_: str = Field(..., description="指令内容")
    appid_: str = Field(..., description="应用ID")
    control_data: List[ComparisonOperationData] = Field(..., description="对照组数据")
    experiment_data: List[ComparisonOperationData] = Field(
        ..., description="实验组数据"
    )


class VersionComparisonResponse(BaseModel):
    """版本对比响应模型"""

    control_version_id: str = Field(..., description="对照组版本ID")
    experiment_version_id: str = Field(..., description="实验组版本ID")
    common_origin_ids: List[str] = Field(..., description="有状态差异的origin_id列表")
    comparison_data: List[VersionComparisonItem] = Field(
        ..., description="对比数据（只包含状态不同的operations）"
    )
    total: int = Field(..., description="有状态差异的origin_id总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")

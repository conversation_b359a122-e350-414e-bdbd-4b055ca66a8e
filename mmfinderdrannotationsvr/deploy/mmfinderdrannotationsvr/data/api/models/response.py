from typing import TypeVar, Generic, Optional, Any
from pydantic.generics import GenericModel

T = TypeVar("T")

class StandardResponse(GenericModel, Generic[T]):
    code: int = 0  # 0 表示成功，非0表示各种错误码
    msg: str = "success"  # 错误信息
    data: Optional[T] = None  # 实际数据

class ErrorCode:
    SUCCESS = 0
    VALIDATION_ERROR = 1001
    NOT_FOUND = 1002
    PERMISSION_DENIED = 1003
    BUSINESS_ERROR = 2001
    SYSTEM_ERROR = 5001